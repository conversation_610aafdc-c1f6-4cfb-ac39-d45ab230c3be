package br.com.pactosolucoes.commons.web.security.impl;

import java.util.Objects;

import org.springframework.stereotype.Service;

import br.com.pactosolucoes.commons.data.dto.DefaultConfigurationDTO;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public @Service class RequestServiceImpl implements RequestService  {

	private final ThreadLocal<DefaultConfigurationDTO> defaulConfigurationTL;

    public RequestServiceImpl() {
    	defaulConfigurationTL = new ThreadLocal<>();
    }

	@Override
	public DefaultConfigurationDTO getCurrentConfiguration() {
		return defaulConfigurationTL.get();
	}

	@Override
	public void updateCurrentConfiguration(DefaultConfigurationDTO defaultConfigurationDTO) {
		defaulConfigurationTL.set(defaultConfigurationDTO);
	}

}
