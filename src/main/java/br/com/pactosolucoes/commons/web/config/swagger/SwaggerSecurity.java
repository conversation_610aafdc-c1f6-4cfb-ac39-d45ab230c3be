package br.com.pactosolucoes.commons.web.config.swagger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.info.BuildProperties;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

@EnableWebSecurity
public class SwaggerSecurity extends WebSecurityConfigurerAdapter {

	@Autowired(required = false)
	private BuildProperties buildProperties;

	@Value("${app.swagger.username:admin}")
	private String swaggerUsername;

	@Value("${app.swagger.password:admin}")
	private String swaggerPassword;


	@Override
	protected void configure(HttpSecurity http) throws Exception {
		http
			.csrf().disable()
			.authorizeRequests()
			.antMatchers("/v2/api-docs/**", "/swagger-ui.html")
			.authenticated()
			.and().httpBasic();
	}


     @Override
     protected void configure(AuthenticationManagerBuilder auth) throws Exception {
         String username = getSwaggerUsername();
         String password = getSwaggerPassword();

         auth
             .inMemoryAuthentication()
                 .withUser(username)
                 .password("{noop}".concat(password))
             .roles("USER");
     }

     private String getSwaggerUsername() {
         if (buildProperties != null) {
             return buildProperties.getArtifact();
         }
         return swaggerUsername;
     }

     private String getSwaggerPassword() {
         if (buildProperties != null) {
             return buildProperties.getArtifact();
         }
         return swaggerPassword;
     }
}