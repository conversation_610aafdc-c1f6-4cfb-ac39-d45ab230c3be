package br.com.pactosolucoes.commons.web.security.contract;

import com.auth0.jwt.interfaces.DecodedJWT;
import br.com.pactosolucoes.commons.data.dto.DefaultConfigurationDTO;

public interface JWTTokenService {

    String EMITENTE = "aut_pacto";
    String CLAIM_CONTENT = "content";
    String PROP_CHAVE = "k";
    String PROP_CHAVE_TREINO = "chave";
    String PROP_USERNAME = "u";
    String PROP_USERNAME_TREINO = "username";
    String PROP_PERFIL_ZW = "pz";
    String PROP_PERFIL_TR = "pt";
    String PROP_COD_ZW = "cz";
    String PROP_COD_TR = "ct";
    String PROP_COD_EMP = "emp";
    String PROP_COD_EMP_TREINO = "empresas";
    String PROP_NOME_PERFIL_OAMD = "np";

    DefaultConfigurationDTO recoveryUser(String token) throws Exception;

    boolean isValid(String token);
    
    DecodedJWT decodeJWT(String token) throws Exception;
}

