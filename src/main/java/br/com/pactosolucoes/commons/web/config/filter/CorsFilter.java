package br.com.pactosolucoes.commons.web.config.filter;

import static br.com.pactosolucoes.utils.enums.CorsFilterKeysEnum.AUTHORIZATION;
import static br.com.pactosolucoes.utils.enums.CorsFilterKeysEnum.COMPANY_ID;
import static br.com.pactosolucoes.utils.enums.CorsFilterKeysEnum.COMPANY_KEY;
import static br.com.pactosolucoes.utils.enums.CorsFilterKeysEnum.LOCALE;
import static br.com.pactosolucoes.utils.enums.CorsFilterKeysEnum.USERNAME;
import static java.lang.Integer.parseInt;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static javax.servlet.http.HttpServletResponse.SC_BAD_REQUEST;
import static javax.servlet.http.HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
import static javax.servlet.http.HttpServletResponse.SC_NOT_ACCEPTABLE;
import static javax.servlet.http.HttpServletResponse.SC_UNAUTHORIZED;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import javax.annotation.PostConstruct;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import br.com.pactosolucoes.commons.data.dto.DefaultConfigurationDTO;
import br.com.pactosolucoes.commons.web.security.contract.JWTTokenService;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class CorsFilter extends OncePerRequestFilter {

  private static final int DEFAULT_COMPANY_ID = -1;

  private static final List<String> FREE_URL_LIST = new ArrayList<>();
  private static final List<String> FREE_METHODS_LIST = new ArrayList<>();
  private static final List<String> BLOCKED_URL_LIST = new ArrayList<>();

  @Autowired
  private JWTTokenService jwtService;

  @Autowired
  private RequestService requestService;

  @Value("${spring.profiles.active}")
  private String activeProfile;

  @Value("${app.config.security.allow-origin-list:*}")
  private String locationAllowOrigin;

  @Value("${app.config.security.url-free:actuator,health}")
  private String urlFreeList;

  @Value("${app.config.security.url-blocked:swagger}")
  private String urlBlockedList;

  @Value("${app.config.free-methods:OPTIONS}")
  private String freeMethods;



  private final String HEADER_ZW_JS_ID = "X-ZWJSID";
  
  @PostConstruct
  private void loadFreeUrls() {
    Arrays.asList(urlFreeList.split(",")).forEach(FREE_URL_LIST::add);
    Arrays.asList(urlBlockedList.split(",")).forEach(BLOCKED_URL_LIST::add);
    Arrays.asList(freeMethods.split(",")).forEach(FREE_METHODS_LIST::add);
  }


  @Override
  protected void doFilterInternal(HttpServletRequest request,
      HttpServletResponse response, FilterChain filterChain)
      throws ServletException, IOException {

    initHeaders(response);

    try {
      checkin(request, response, filterChain);
    } catch (IllegalArgumentException e) {
      return;
    }

    continueRequest(request, response, filterChain);
  }


  private void initHeaders(HttpServletResponse response) {
    response.setHeader("Access-Control-Allow-Origin",
        locationAllowOrigin.trim());

    response.setHeader("Access-Control-Allow-Methods",
        "GET, POST, PUT, PATCH, DELETE, OPTIONS");

    response.setHeader("Access-Control-Max-Age", "3600");

    String allowedHeaders = "authorization, content-type, xsrf-token, Authorization, empresaId, " + HEADER_ZW_JS_ID;

    response.setHeader("Access-Control-Allow-Headers", allowedHeaders);

    response.addHeader("Access-Control-Expose-Headers",
        "xsrf-token, location, uuid, id");
  }

  private void checkin(HttpServletRequest request,
      HttpServletResponse response, FilterChain filterChain)
      throws IOException, ServletException {

    if (isBlocked(request)) {
      response.setStatus(SC_UNAUTHORIZED);
      throw new IllegalArgumentException();
    }

    if (isFree(request, response, filterChain)) {
      filterChain.doFilter(request, response);
      throw new IllegalArgumentException();
    }
  }

  private boolean isBlocked(HttpServletRequest request) {
    if (!activeProfile.equalsIgnoreCase("prd")) {
      return Boolean.FALSE;
    }

    return BLOCKED_URL_LIST.stream()
        .filter(item -> request.getRequestURI().contains(item)).count() > 0;
  }

  private boolean isFree(HttpServletRequest request,
      HttpServletResponse response, FilterChain filterChain)
      throws IOException, ServletException {
    Boolean isFree = Boolean.FALSE;

    if (FREE_METHODS_LIST.contains(request.getMethod())) {
      isFree = Boolean.TRUE;
    }

    if (FREE_URL_LIST.stream()
        .filter(url -> request.getRequestURI().endsWith(url)).findFirst()        
        .isPresent()) {
      isFree = Boolean.TRUE;
    }

    return isFree;
  }

  private void continueRequest(HttpServletRequest request,
      HttpServletResponse response,
      FilterChain filterChain) {
    try {

      if (checkIfCanContinue(request)) {

        loadRequestConfiguration((HttpServletRequest) request,
            (HttpServletResponse) response);
        loadCompanyFromSession();
        filterChain.doFilter(request, response);

      } else {
        log.info("User has no permission to access the url "
            .concat(request.getRequestURL().toString()));
        response.setStatus(SC_UNAUTHORIZED);
      }
    } catch (IllegalArgumentException e) {
      e.printStackTrace();
      log.error("Request Error", e);
      return;

    } catch (Exception e) {
      e.printStackTrace();
      log.error("Request error", e);
      response.setStatus(SC_INTERNAL_SERVER_ERROR);
    }
  }

  private boolean checkIfCanContinue(HttpServletRequest request) {
    boolean isZw = checkIfIsZw(request);
    String token = getToken(request);
    boolean jwtIsValid = jwtService.isValid(token);
    boolean prd = activeProfile.equalsIgnoreCase("prd");

    log.debug("Checking if can continue: token = "+ token);
    log.debug("Checking if can continue: activeProfile = "+ activeProfile);
    log.debug("Checking if can continue: checkIfIsZw = "+ isZw);
    log.debug("Checking if can continue: jwtIsValid = "+jwtIsValid);

    return !prd
        || isZw
        || jwtIsValid;
  }

  private boolean checkIfIsZw(HttpServletRequest request) {
    return nonNull(request.getHeader("SISTEMA_ZW"))
        && request.getHeader("SISTEMA_ZW").equalsIgnoreCase("true");
  }

  private void loadRequestConfiguration(HttpServletRequest request,
      HttpServletResponse response)
      throws Exception {
    DefaultConfigurationDTO defaultConfigurationDTO = null;
    String token = getToken(request);


    if (checkIfIsZw(request)) {
      defaultConfigurationDTO = initDefaultConfigZw(request, response);
    } else {
      defaultConfigurationDTO = initiDefaultConfigWithJWT(request, token);
    }

    requestService.updateCurrentConfiguration(defaultConfigurationDTO);
  }

  private DefaultConfigurationDTO initDefaultConfigZw(
      HttpServletRequest request, HttpServletResponse response) {

    String companyKey = request.getHeader(COMPANY_KEY.getValue());

    if (Objects.isNull(companyKey)) {
      response.setStatus(SC_NOT_ACCEPTABLE);
      throw new IllegalArgumentException(
          "Veio pelo ZW e a chave está vazia. Retornara um 406 (Não aceito).");
    }

    return DefaultConfigurationDTO
        .builder()
        .companyKey(companyKey)
        .companyId(getCompanyId(request))
        .username(getUserName(request))
        .build();
  }

  private DefaultConfigurationDTO initiDefaultConfigWithJWT(
      HttpServletRequest request, String token) throws Exception {

    DefaultConfigurationDTO defaultConfigurationDTO = null;

    if (activeProfile.equalsIgnoreCase("prd") | jwtService.isValid(token)) {
      defaultConfigurationDTO = jwtService.recoveryUser(token);
      defaultConfigurationDTO.setCompanyId(getCompanyId(request));
    }

    if (Objects.isNull(token) || !jwtService.isValid(token)) {
      // Criar configuração básica para desenvolvimento
      defaultConfigurationDTO = DefaultConfigurationDTO.builder()
          .token(token)
          .companyKey("mock")
          .companyId(getCompanyId(request))
          .username("mock-user")
          .build();
    }

    return defaultConfigurationDTO;
  }

  private void loadCompanyFromSession() {
    // Método simplificado - não carrega mais dados de empresa
  }


  private String getToken(HttpServletRequest request) {
    String token = request.getHeader(AUTHORIZATION.getValue());

    if (nonNull(token)) {
      token = token.trim();
      token = token.contains(" ") && token.split(" ").length > 1
          ? token.split(" ")[1]
          : token;
    }

    return token;
  }

  private Integer getCompanyId(HttpServletRequest request) {
    String companyId = request.getHeader(COMPANY_ID.getValue());
    return isNull(companyId) ? DEFAULT_COMPANY_ID : parseInt(companyId.trim());
  }

  private String getUserName(HttpServletRequest request) {
    String username = request.getHeader(USERNAME.getValue());
    return isNull(username) ? "" : username.trim();
  }
}
