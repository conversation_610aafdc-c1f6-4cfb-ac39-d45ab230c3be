package br.com.pactosolucoes.commons.web.security.impl;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;

import br.com.pactosolucoes.commons.data.dto.DefaultConfigurationDTO;

class JWTTokenServiceImplTest {

    private JWTTokenServiceImpl jwtTokenService;
    private String secretKey = "test-secret-key";
    private Algorithm algorithm;

    @BeforeEach
    void setUp() {
        jwtTokenService = new JWTTokenServiceImpl();
        ReflectionTestUtils.setField(jwtTokenService, "tokenSecret", secretKey);
        algorithm = Algorithm.HMAC256(secretKey);
        // Usar reflection para chamar o método init privado
        ReflectionTestUtils.invokeMethod(jwtTokenService, "init");
    }

    @Test
    void testIsValid_WithValidToken_ShouldReturnTrue() {
        // Arrange
        String validToken = JWT.create()
                .withIssuer("aut_pacto")
                .withClaim("content", "{\"type\":\"user\",\"u\":\"testuser\",\"k\":\"testkey\"}")
                .sign(algorithm);

        // Act
        boolean result = jwtTokenService.isValid(validToken);

        // Assert
        assertTrue(result);
    }

    @Test
    void testIsValid_WithInvalidToken_ShouldReturnFalse() {
        // Arrange
        String invalidToken = "invalid.token.here";

        // Act
        boolean result = jwtTokenService.isValid(invalidToken);

        // Assert
        assertFalse(result);
    }

    @Test
    void testRecoveryUser_WithValidToken_ShouldReturnConfiguration() throws Exception {
        // Arrange
        String validToken = JWT.create()
                .withIssuer("aut_pacto")
                .withClaim("content", "{\"type\":\"user\",\"u\":\"testuser\",\"k\":\"testkey\",\"emp\":123}")
                .sign(algorithm);

        // Act
        DefaultConfigurationDTO result = jwtTokenService.recoveryUser(validToken);

        // Assert
        assertNotNull(result);
        assertEquals(validToken, result.getToken());
        assertEquals("testuser", result.getUsername());
        assertEquals("testkey", result.getCompanyKey());
        assertEquals(123, result.getCompanyId().intValue());
    }

    @Test
    void testRecoveryUser_WithOAMDUserToken_ShouldReturnOAMDConfiguration() throws Exception {
        // Arrange
        String oamdToken = JWT.create()
                .withIssuer("aut_pacto")
                .withClaim("content", "{\"type\":\"user_oamd\",\"u\":\"oamduser\",\"np\":\"admin\"}")
                .sign(algorithm);

        // Act
        DefaultConfigurationDTO result = jwtTokenService.recoveryUser(oamdToken);

        // Assert
        assertNotNull(result);
        assertEquals(oamdToken, result.getToken());
        assertEquals("oamduser", result.getUsername());
        assertEquals("admin", result.getNomePerfilOamd());
        assertTrue(result.isUserOamd());
        assertEquals(0, result.getCompanyId().intValue());
    }
}
