package br.com.pactosolucoes.commons.data.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DefaultConfigurationDTO {

    private Integer zwId;
    private Integer treinoId;
    private Integer treinoProfile;
    private Integer zwProfile;
    private String 	companyKey;
    private String 	username;
    private Integer companyId;
    private String 	token;
    private boolean userOamd;
    private String nomePerfilOamd;

    public Integer getCompanyId() {
    	if(this.companyId == null || this.companyId < 0) {
    		throw new RuntimeException("Header empresaId precisa ser informado");
    	}
    	return companyId;
    }

}
