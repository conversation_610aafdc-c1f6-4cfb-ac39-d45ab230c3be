package br.com.pactosolucoes.commons.config;

import static org.springframework.context.annotation.ScopedProxyMode.TARGET_CLASS;
import static org.springframework.web.context.WebApplicationContext.SCOPE_REQUEST;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import br.com.pactosolucoes.commons.web.security.impl.RequestServiceImpl;

public @Configuration class BeansInitializer {

	@Bean
	@Scope(value = SCOPE_REQUEST, proxyMode = TARGET_CLASS)
	public RequestService requestService() {
		return new RequestServiceImpl();
	}

}
