<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.1</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
    <groupId>br.com.pactosolucoes</groupId>
    <artifactId>pacto-commons-auth</artifactId>
    <version>0.0.1</version>
	<name>Pacto Commons Auth</name>
	<description>Projeto comum para os microserviços da Pacto Soluções</description>


	<developers>
		<developer>
			<name><PERSON></name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<!-- start nexus configuration -->
	<repositories>
		<repository>
			<releases>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>never</updatePolicy>
			</snapshots>
			<id>maven-public</id>
			<url>https://mussum.ath.cx/nexus/content/groups/public/</url>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>pacto</id>
			<url>https://mussum.ath.cx/nexus/content/groups/public/</url>
		</pluginRepository>
	</pluginRepositories>
	<distributionManagement>
		<repository>
			<id>maven-releases</id>
			<url>https://mussum.ath.cx/nexus/content/repositories/releases/</url>
		</repository>
		<snapshotRepository>
			<id>maven-snapshots</id>
			<url>https://mussum.ath.cx/nexus/content/repositories/snapshots/</url>
		</snapshotRepository>
	</distributionManagement>

	<scm>
		<connection>scm:git:gitlab.com/Plataformazw/flash/pacto-commons.git</connection>
		<developerConnection>scm:git:gitlab.com/Plataformazw/flash/pacto-commons.git</developerConnection>
		<url>https://gitlab.com:Plataformazw/flash/pacto-commons.git</url>
	  	<tag>v0.0.10</tag>
  	</scm>

	<!-- finish nexus configuration -->

	<properties>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven.compiler.target>1.8</maven.compiler.target>
		<maven.compiler.source>1.8</maven.compiler.source>

		<pacto.utils.version>0.0.1</pacto.utils.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>br.com.pactosolucoes</groupId>
			<artifactId>pacto-utils</artifactId>
			<version>${pacto.utils.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		


		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- INICIO SPRING -->


		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-config</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-autoconfigure</artifactId>
		</dependency>

		<!-- Servlet API -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- Logging -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>

		<!-- JSON -->
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20220320</version>
		</dependency>
		<!-- FIM SPRING -->

		<!-- https://mvnrepository.com/artifact/com.auth0/java-jwt -->
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.19.1</version>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>

    </dependencies>

	<build>
		<sourceDirectory>src/main/java</sourceDirectory>
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
				</includes>
			</resource>
		</resources>
		<pluginManagement>
			<plugins>
				<plugin>
					<artifactId>maven-source-plugin</artifactId>
					<version>2.8.1</version>
					<executions>
						<execution>
							<phase>deploy</phase>
							<goals>
								<goal>jar</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<artifactId>maven-deploy-plugin</artifactId>
					<version>2.8.1</version>
					<executions>
						<execution>
							<id>default-deploy</id>
							<phase>deploy</phase>
							<goals>
								<goal>deploy</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>org.sonatype.plugins</groupId>
					<artifactId>nexus-staging-maven-plugin</artifactId>
					<version>1.5.1</version>
					<executions>
						<execution>
							<id>default-deploy</id>
							<phase>deploy</phase>
							<goals>
								<goal>deploy</goal>
							</goals>
						</execution>
					</executions>
					<configuration>
						<serverId>nexus</serverId>
						<nexusUrl>https://mussum.ath.cx/nexus/</nexusUrl>
						<skipStaging>true</skipStaging>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-release-plugin</artifactId>
					<version>3.0.0-M7</version>
					<configuration>
						<tagNameFormat>v@{project.version}</tagNameFormat>
						<releaseProfiles>releases</releaseProfiles>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<finalName>${project.artifactId}</finalName>
	</build>
	<profiles>
		<profile>
			<id>releases</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.sonatype.plugins</groupId>
						<artifactId>nexus-staging-maven-plugin</artifactId>
						<version>1.5.1</version>
						<executions>
							<execution>
								<id>default-deploy</id>
								<phase>deploy</phase>
								<goals>
									<goal>deploy</goal>
								</goals>
							</execution>
						</executions>
						<configuration>
							<serverId>nexus-releases</serverId>
							<nexusUrl>https://mussum.ath.cx/nexus/</nexusUrl>
							<skipStaging>true</skipStaging>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
</project>
