# Configuração CodeRabbit otimizada para projetos Java - Foco em Potential Issues
language: "pt-BR"
tone_instructions: "Seja direto e foque EXCLUSIVAMENTE em potential issues: bugs, vulnerabilidades de segurança, memory leaks, null pointer exceptions, race conditions e problemas de performance. Ignore completamente estilo, formatação e sugestões de refatoração."

reviews:
  # Perfil assertivo para detectar mais potential issues
  profile: "chill"

  # Configurações otimizadas para focar apenas em potential issues críticos
  high_level_summary: false
  changed_files_summary: false
  sequence_diagrams: false
  poem: true
  suggested_labels: false
  estimate_code_review_effort: true
  assess_linked_issues: false
  related_issues: true
  related_prs: true
  suggested_reviewers: true
  collapse_walkthrough: true

  # Desabilitar funcionalidades que não são potential issues
  finishing_touches:
    docstrings:
      enabled: false
    unit_tests:
      enabled: false

  # Configurações de auto review para branches principais
  auto_review:
    enabled: true
    auto_incremental_review: true
    drafts: false
    base_branches:
      - master
      - main
      - development
      - develop

  # Filtros de caminho para ignorar arquivos gerados e desnecessários
  path_filters:
    # Arquivos gerados automaticamente
    - "!**/*.generated.java"
    - "!**/*Generated.java"
    - "!**/target/**"
    - "!**/build/**"

    # Diretórios de dependências e build
    - "!**/.mvn/**"
    - "!**/node_modules/**"

    # Arquivos de configuração que não precisam de revisão
    - "!.coderabbit.yaml"

  # Instruções específicas para Java focadas em potential issues
  path_instructions:
    - path: "**/*.java"
      instructions: |
        FOQUE EXCLUSIVAMENTE EM POTENTIAL ISSUES CRÍTICOS:

        🐛 BUGS E ERROS:
        - Null pointer exceptions (NPE) - verificar dereferenciamento de objetos null
        - ArrayIndexOutOfBoundsException - verificar acesso a arrays
        - ClassCastException - verificar casting inseguro
        - ConcurrentModificationException - verificar modificação concorrente de coleções
        - Resource leaks - verificar fechamento de streams, connections, files
        - Infinite loops ou recursão infinita
        - Off-by-one errors em loops e arrays

        🔒 SEGURANÇA:
        - SQL injection em queries dinâmicas
        - Path traversal em manipulação de arquivos
        - Desserialização insegura
        - Uso inseguro de Random vs SecureRandom
        - Hardcoded credentials (exceto secrets já detectados)
        - XML External Entity (XXE) vulnerabilities

        ⚡ PERFORMANCE E MEMÓRIA:
        - Memory leaks - objetos não liberados, listeners não removidos
        - String concatenation em loops (usar StringBuilder)
        - Uso desnecessário de synchronized que pode causar deadlocks
        - Collections não otimizadas para o caso de uso
        - Autoboxing excessivo em loops

        🧵 CONCORRÊNCIA:
        - Race conditions em código multi-thread
        - Deadlocks potenciais
        - Uso incorreto de volatile
        - Double-checked locking incorreto
        - Shared mutable state sem sincronização

        ❌ NÃO COMENTE SOBRE:
        - Estilo de código, formatação, naming conventions
        - Refatorações que não corrigem bugs
        - Otimizações prematuras
        - Padrões de design que funcionam corretamente
        - Atualizações de bibliotecas a menos que sejam correções de bugs ou segurança
        - Arquivos gerados automaticamente
        - Sugestões de refatoração em classes, métodos ou estruturas a menos que resolvam problemas de funcionalidade ou performance significativas

    - path: "**/pom.xml"
      instructions: |
        FOQUE EM POTENTIAL ISSUES DE DEPENDÊNCIAS:
        - Vulnerabilidades conhecidas em versões de dependências
        - Conflitos de versões que podem causar runtime errors
        - Dependências com escopo incorreto que podem causar ClassNotFoundException
        - Dependências transitivas problemáticas

    - path: "**/*.properties"
      instructions: |
        FOQUE EM POTENTIAL ISSUES DE CONFIGURAÇÃO:
        - Configurações que podem causar falhas em runtime
        - URLs ou paths hardcoded que podem falhar em diferentes ambientes
        - Configurações de timeout muito baixas que podem causar falhas
        - Configurações de pool de conexões inadequadas

  # Ferramentas de análise estática habilitadas (baseadas no schema oficial)
  tools:
    # Análise de código Java
    pmd:
      enabled: true
    # Análise de segurança
    semgrep:
      enabled: true
    # Análise geral
    gitleaks:
      enabled: true
    # Desabilitar ferramentas não relacionadas a Java
    eslint:
      enabled: false
    ruff:
      enabled: false

# Configurações de base de conhecimento otimizadas para Java
knowledge_base:
  # Desabilitar para focar apenas em potential issues detectados pelo CodeRabbit
  opt_out: false
  web_search:
    enabled: true
  code_guidelines:
    enabled: true
    filePatterns:
      - "**/checkstyle.xml"
      - "**/pmd.xml"
      - "**/spotbugs.xml"
      - "**/sonar-project.properties"
      - "**/README.md"
      - "**/SECURITY.md"
  learnings:
    scope: "local"
  issues:
    scope: "local"
