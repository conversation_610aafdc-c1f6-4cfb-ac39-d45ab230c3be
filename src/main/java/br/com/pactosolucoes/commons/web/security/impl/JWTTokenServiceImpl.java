package br.com.pactosolucoes.commons.web.security.impl;


import java.util.Objects;
import javax.annotation.PostConstruct;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import br.com.pactosolucoes.commons.data.dto.DefaultConfigurationDTO;
import br.com.pactosolucoes.commons.web.security.contract.JWTTokenService;
import br.com.pactosolucoes.utils.FileUteis;

@Service
public class JWTTokenServiceImpl implements JWTTokenService {

	@Value("${secret.key.value}")
    private String tokenSecret;
    
    private JWTVerifier verificador;
    private Algorithm algoritimo;
    
    @Value("${secret.key.path}")
    private String secretKeyPath;
    
    @PostConstruct
    private void init() {
    	this.algoritimo = Algorithm.HMAC256(secret());
    	this.verificador = JWT.require(algoritimo).withIssuer(EMITENTE).build();
    }
    
    @Override
    public DefaultConfigurationDTO recoveryUser(String token) throws Exception {
        try{
            JSONObject content = new JSONObject(getClaim(String.class, decodeJWT(token), JWTTokenService.CLAIM_CONTENT));

            if (content.optString("type").equals("user_oamd")) {
                return DefaultConfigurationDTO
                        .builder()
                        .token(token)
                        .userOamd(true)
                        .username(content.optString(JWTTokenService.PROP_USERNAME))
                        .nomePerfilOamd(content.optString(JWTTokenService.PROP_NOME_PERFIL_OAMD))
                        .companyId(0)
                        .build();
            }

            return DefaultConfigurationDTO
                    .builder()
                        .token(token)
                        .companyKey(content.optString(JWTTokenService.PROP_CHAVE))
                        .zwId(content.optInt(JWTTokenService.PROP_COD_ZW))
                        .zwProfile(content.optInt(JWTTokenService.PROP_PERFIL_ZW))
                        .treinoId(content.optInt(JWTTokenService.PROP_COD_TR))
                        .treinoProfile(content.optInt(JWTTokenService.PROP_PERFIL_TR))
                        .companyId(content.optInt(JWTTokenService.PROP_COD_EMP))
                        .username(content.optString(JWTTokenService.PROP_USERNAME))
                    .build();
        }catch(NullPointerException e){
            DecodedJWT decodedJWT = decodeJWT(token);
            return DefaultConfigurationDTO
                    .builder()
                    .token(token)
                    .companyKey(decodedJWT.getClaim(JWTTokenService.PROP_CHAVE_TREINO).asString())
                    .zwId(decodedJWT.getClaim(JWTTokenService.PROP_COD_ZW).asInt())
                    .zwProfile(decodedJWT.getClaim(JWTTokenService.PROP_PERFIL_ZW).asString() == null ? 0 : Integer.parseInt(decodedJWT.getClaim(JWTTokenService.PROP_PERFIL_ZW).asString()))
                    .treinoId(decodedJWT.getClaim(JWTTokenService.PROP_COD_TR).asInt())
                    .treinoProfile(decodedJWT.getClaim(JWTTokenService.PROP_PERFIL_TR).asString() == null ? 0 : Integer.parseInt(decodedJWT.getClaim(JWTTokenService.PROP_PERFIL_TR).asString()))
                    .companyId(Integer.parseInt(decodedJWT.getClaim(JWTTokenService.PROP_COD_EMP_TREINO).asString()))
                    .username(decodedJWT.getClaim(JWTTokenService.PROP_USERNAME_TREINO).asString())
                    .build();
        }
    }
    
	@Override
    public boolean isValid(String token){
    	boolean isValid = true;
    	
        try {
			decodeJWT(token);
		} catch (Exception e) {
			isValid = false;
		}
        
        return isValid;
    }

    private <T> T getClaim(Class<T> tipo, DecodedJWT decodedJWT, String claimCode) {
        Claim claim = decodedJWT.getClaims().get(claimCode);
        
        return Objects.nonNull(claim) ? claim.as(tipo) : null;
    }

    private String secret() {
        if (Objects.isNull(tokenSecret))
            tokenSecret = FileUteis.readFile(secretKeyPath);

    	return tokenSecret;
    }

	@Override
	public DecodedJWT decodeJWT(String token) throws Exception {
		return verificador.verify(token);
	}

}
